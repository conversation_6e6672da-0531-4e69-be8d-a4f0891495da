import React, { memo, useState } from 'react'
import { View, TouchableOpacity, StyleSheet, Dimensions, Platform } from 'react-native'
import { Colors, Mixins, MyText, SearchInput } from '@react-native-xwork'
import { Image } from '@mwg-kits/components'
import IMAGES from '../../assets/images'

const WIDTH = Dimensions.get('window').width

interface ComponentLeftProps {
    name: string
    onGoBack: () => void
}

export const ComponentLeft = memo(({ name, onGoBack }: ComponentLeftProps) => {
    return (
        <View style={styles.leftContainer}>
            <TouchableOpacity hitSlop={20} onPress={onGoBack}>
                <Image
                    //@ts-ignore
                    isLocal
                    source={{ uri: 'ic_left_arrow' }}
                    style={styles.backIcon}
                />
            </TouchableOpacity>
            <MyText category="bold.h6" text={name} style={styles.titleLeft} />
        </View>
    )
})

interface ComponentRightProps {
    onToggleSearch: () => void
}

export const ComponentRight = memo(({ onToggleSearch }: ComponentRightProps) => {
    return (
        <TouchableOpacity onPress={onToggleSearch}>
            <Image
                //@ts-ignore
                isLocal
                source={IMAGES.ic_search}
                style={styles.searchIcon}
            />
        </TouchableOpacity>
    )
})

interface ComponentLeftSearchProps {
    searchText: string
    onToggleSearch: () => void
    onSearchSubmit: (text: string) => void
    onClearSearch: () => void
    totalResults?: number
    currentIndex?: number
    onNavigateNext?: () => void
    onNavigatePrev?: () => void
}

export const ComponentLeftSearch = memo(
    ({
        searchText,
        onToggleSearch,
        onSearchSubmit,
        onClearSearch,
        totalResults = 0,
        currentIndex = 0,
        onNavigateNext,
        onNavigatePrev
    }: ComponentLeftSearchProps) => {
        const [text, setText] = useState(searchText)

        const handleBackPress = () => {
            onToggleSearch()
            setText('')
            if (text.trim() !== '') {
                onClearSearch()
            }
        }

        return (
            <View style={styles.searchContainer}>
                <TouchableOpacity hitSlop={20} onPress={handleBackPress}>
                    <Image
                        //@ts-ignore
                        isLocal
                        source={{ uri: 'ic_left_arrow' }}
                        style={styles.searchBackIcon}
                    />
                </TouchableOpacity>
                <View style={styles.searchInputWrapper}>
                    <SearchInput
                        placeholder="Nhập từ khóa tìm kiếm tin nhắn..."
                        isShowSearch={false}
                        isOutline={true}
                        containerStyle={styles.searchInputContainer}
                        heightInput={Mixins.scale(32)}
                        fontSize={12}
                        customSearchBarStyles={styles.searchBarStyles}
                        value={text}
                        onChangeText={(text) => setText(text)}
                        onSubmitEditing={() => onSearchSubmit(text)}
                        onClear={() => {
                            setText('')
                            onClearSearch()
                        }}
                    />
                </View>
                {totalResults > 0 && (
                    <View style={styles.navigationContainer}>
                        <MyText
                            category="body.2"
                            text={`${currentIndex + 1}/${totalResults}`}
                            style={styles.resultCount}
                        />
                        <View style={styles.navigationButtons}>
                            <TouchableOpacity
                                onPress={onNavigatePrev}
                                disabled={currentIndex === 0}
                                style={[styles.navButton, currentIndex === 0 && styles.navButtonDisabled]}
                            >
                                <Image
                                    //@ts-ignore
                                    isLocal
                                    source={IMAGES.ic_chevron_up}
                                    style={styles.navIcon}
                                />
                            </TouchableOpacity>
                            <TouchableOpacity
                                onPress={onNavigateNext}
                                disabled={currentIndex === totalResults - 1}
                                style={[styles.navButton, currentIndex === totalResults - 1 && styles.navButtonDisabled]}
                            >
                                <Image
                                    //@ts-ignore
                                    isLocal
                                    source={IMAGES.ic_chevron_down}
                                    style={styles.navIcon}
                                />
                            </TouchableOpacity>
                        </View>
                    </View>
                )}
            </View>
        )
    }
)

const styles = StyleSheet.create({
    leftContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: Mixins.scale(8)
    },
    backIcon: {
        width: 32,
        height: 32,
        resizeMode: 'contain',
        tintColor: Colors.BUTTON_LINK_BRAND_ICON
    },
    titleLeft: {
        color: Colors.TEXT_PRIMARY,
        maxWidth: Mixins.scale(300),
        fontSize: Mixins.scaleFont(20),
        ...(Platform.OS === 'android' ? { fontWeight: '700' } : {})
    },
    searchIcon: {
        width: Mixins.scale(24),
        height: Mixins.scale(24)
    },
    searchContainer: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        gap: Mixins.scale(8),
        width: WIDTH - Mixins.scale(32)
    },
    searchBackIcon: {
        width: 24,
        height: 24,
        resizeMode: 'contain',
        tintColor: Colors.BUTTON_LINK_BRAND_ICON
    },
    searchInputWrapper: {
        flex: 1
    },
    searchInputContainer: {
        flex: 1
    },
    searchBarStyles: {
        borderWidth: 0,
        paddingHorizontal: 0,
        backgroundColor: 'transparent'
    },
    navigationContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: Mixins.scale(8)
    },
    navigationButtons: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: Mixins.scale(8)
    },
    navButton: {
        padding: Mixins.scale(4),
        marginHorizontal: Mixins.scale(2)
    },
    navButtonDisabled: {
        opacity: 0.5
    },
    navIcon: {
        width: Mixins.scale(20),
        height: Mixins.scale(20),
        tintColor: Colors.BUTTON_LINK_BRAND_ICON
    },
    resultCount: {
        fontSize: Mixins.scaleFont(12),
        color: Colors.TEXT_PRIMARY
    }
})
